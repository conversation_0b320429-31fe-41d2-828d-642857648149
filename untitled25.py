# -*- coding: utf-8 -*-
"""Untitled25.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1sBkQtlpSezpNZupG6SSbYnpMvy6ltKei
"""

import pandas as pd

df1 = pd.read_json('/content/wa1.json')
df2 = pd.read_json('/content/wa2.json')

columns_to_keep = ["_ao3e", "x1iyjqo2"]
df1 = df1[columns_to_keep]
df2 = df2[columns_to_keep]

df1.rename(columns={'_ao3e': 'number1', 'x1iyjqo2': 'number2'}, inplace=True)
df2.rename(columns={'_ao3e': 'number1', 'x1iyjqo2': 'number2'}, inplace=True)

def filter_and_combine_columns(df, col1, col2, prefix="+92"):
    filtered_col1 = df[col1][df[col1].str.startswith(prefix, na=False)]
    filtered_col2 = df[col2][df[col2].str.startswith(prefix, na=False)]
    combined = pd.concat([filtered_col1, filtered_col2], ignore_index=True)
    return pd.DataFrame({'combined_column': combined})

df1_filtered = filter_and_combine_columns(df1, 'number1', 'number2')
df2_filtered = filter_and_combine_columns(df2, 'number1', 'number2')

df1_filtered['combined_column'] = df1_filtered['combined_column'].astype(str)
df2_filtered['combined_column'] = df2_filtered['combined_column'].astype(str)

df1_filtered['combined_column'] = df1_filtered['combined_column'].str.replace('+92', '0')
df2_filtered['combined_column'] = df2_filtered['combined_column'].str.replace('+92', '0')

df1_filtered['combined_column'] = df1_filtered['combined_column'].str.replace(' ', '')
df2_filtered['combined_column'] = df2_filtered['combined_column'].str.replace(' ', '')

# Merge the dataframes and keep only unique
merged_df = pd.merge(df1_filtered, df2_filtered, on='combined_column', how='outer')

merged_df

import json
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Union

def fetch_data_from_api(number: str) -> Optional[dict]:
    """Fetch data from API for a given number."""
    api_url = f"https://simownerdetails.net.pk/wp-admin/admin-ajax.php?action=get_number_data&get_number_data=searchdata={number}"

    try:
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data for number {number}: {e}")
        return None

def parse_single_result(card: BeautifulSoup) -> Dict[str, str]:
    """Parse a single result card from the HTML content."""
    record = {}
    for field in card.find_all('div', class_='field'):
        label = field.find('label', class_='info')
        value_div = field.find('div')

        if not label or not value_div:
            continue

        label_text = label.get_text(strip=True).replace('#', '').replace(' ', '_').lower()
        value_text = ' '.join(value_div.get_text(strip=True).split())

        if value_text.lower() not in ('', 'unknown', 'record not found'):
            record[label_text] = value_text
    return record

def parse_api_response(api_results: Dict[str, dict]) -> Dict[str, Union[List[dict], dict]]:
    """Parse API responses and extract structured data."""
    final_results = {}

    for number, response in api_results.items():
        if not response.get('success'):
            final_results[number] = {"error": "API request failed"}
            continue

        soup = BeautifulSoup(response['data'], 'html.parser')
        records = [
            parse_single_result(card)
            for card in soup.find_all('div', class_='result-card')
        ]

        final_results[number] = [r for r in records if r]

    return final_results



api_results = {
    number: fetch_data_from_api(number)
    for number in merged_df['combined_column'].unique()
}

final_json = parse_api_response(api_results)
print(json.dumps(final_json, indent=2))